import 'package:flutter/material.dart';
import 'package:wicker/services/config_service.dart';
import 'package:wicker/services/ecommerce_service.dart';
import 'package:wicker/services/post_service.dart';
import 'package:wicker/widgets/neubrutalist_widgets.dart';
import 'package:wicker/widgets/post_card.dart';
import 'package:wicker/widgets/product_card.dart';

class BusinessDetailScreen extends StatefulWidget {
  final Map<String, dynamic> businessData;
  const BusinessDetailScreen({super.key, required this.businessData});

  @override
  State<BusinessDetailScreen> createState() => _BusinessDetailScreenState();
}

class _BusinessDetailScreenState extends State<BusinessDetailScreen>
    with SingleTickerProviderStateMixin {
  final EcommerceService _ecommerceService = EcommerceService();
  final ConfigService _configService = ConfigService.instance;
  final PostService _postService = PostService();
  late TabController _tabController;

  late Future<List<Map<String, dynamic>>> _productsFuture;
  late Future<List<Map<String, dynamic>>> _postsFuture;
  late Future<String> _baseUrlFuture;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);

    final businessId = widget.businessData['_id']['\$oid'];
    final ownerId = widget.businessData['owner_id']['\$oid'];
    _baseUrlFuture = _configService.getBaseUrl();



    _productsFuture = _ecommerceService.getBusinessProducts(businessId);
    _postsFuture = _postService.getPostsByUser(ownerId);
  }

  @override
  Widget build(BuildContext context) {
    final businessName = widget.businessData['name'];
    final businessDescription = widget.businessData['description'];

          bool hasImage =
              businessName['images'] != null && (businessName['photos'] as List).isNotEmpty;
          String imageUrl = '';

          if (hasImage) {
            String imagePath = businessName['photos'][0];
            imageUrl = '$baseUrl/${imagePath.replaceAll('\\', '/')}';
          } else {
            imageUrl =
                'https://picsum.photos/seed/${businessName['_id']['\$oid']}/800/400';
          }





    return Scaffold(
      backgroundColor: const Color(0xFFFEF7F0),
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
                SliverAppBar(
                  expandedHeight: 250.0,
                  floating: false,
                  pinned: true,
                  backgroundColor: const Color(0xFF6C5CE7),
                  leading: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: GestureDetector(
                      onTap: () => Navigator.of(context).pop(),
                      child: const NeuCard(
                        margin: EdgeInsets.zero,
                        padding: EdgeInsets.zero,
                        child: Center(
                          child: Icon(Icons.arrow_back, color: Colors.black),
                        ),
                      ),
                    ),
                  ),
                  flexibleSpace: FlexibleSpaceBar(
                    centerTitle: true,
                    titlePadding: const EdgeInsets.symmetric(
                      vertical: 12,
                      horizontal: 48,
                    ),
                    title: NeuCard(
                      padding: const EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 6,
                      ),
                      margin: EdgeInsets.zero,
                      child: Text(
                        businessName['name'],
                        style: const TextStyle(
                          color: Colors.black,
                          fontWeight: FontWeight.bold,
                          fontSize: 16,
                        ),
                        overflow: TextOverflow.ellipsis,
                      ),
                    ),
                    background: Stack(
                      fit: StackFit.expand,
                      children: [
                        Image.network(imageUrl, fit: BoxFit.cover),
                        Container(
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                              colors: [
                                Colors.transparent,
                                Colors.black.withValues(alpha: 0.4),
                              ],
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
            
          ];
        },
        body: Column(
          children: [
            NeuCard(
              margin: const EdgeInsets.fromLTRB(16, 16, 16, 0),
              padding: EdgeInsets.zero,
              child: TabBar(
                controller: _tabController,
                indicator: const BoxDecoration(
                  color: Color(0xFFFFE66D),
                  border: Border(
                    bottom: BorderSide(color: Colors.black, width: 3.0),
                  ),
                ),
                tabs: const [
                  Tab(icon: Icon(Icons.shopping_cart_outlined), text: 'Shop'),
                  Tab(icon: Icon(Icons.post_add), text: 'Posts'),
                ],
              ),
            ),
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [_buildShopTab(), _buildPostsTab()],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildShopTab() {
    if (_productsFuture == null) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.storefront, size: 48, color: Colors.grey[800]),
              const SizedBox(height: 16),
              const Text(
                'NO SHOP AVAILABLE',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
            ],
          ),
        ),
      );
    }

    return FutureBuilder<List<Map<String, dynamic>>>(
      future: _productsFuture,
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        if (snapshot.hasError) {
          return Center(
            child: Text("Error loading products: ${snapshot.error}"),
          );
        }
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return Center(
            child: NeuCard(
              backgroundColor: const Color(0xFFFFE66D),
              child: const Column(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Icon(Icons.storefront, size: 48),
                  SizedBox(height: 16),
                  Text(
                    'NO PRODUCTS YET',
                    style: TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            ),
          );
        }

        final products = snapshot.data!;
        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: products.length,
          itemBuilder: (context, index) {
            return ProductCard(productData: products[index]);
          },
        );
      },
    );
  }

  Widget _buildPostsTab(List<Map<String, dynamic>> posts) {
    if (posts.isEmpty) {
      return Center(
        child: NeuCard(
          backgroundColor: const Color(0xFFFFE66D),
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(
                Icons.photo_library_outlined,
                size: 48,
                color: Colors.grey[800],
              ),
              const SizedBox(height: 16),
              const Text(
                'NO POSTS YET',
                style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
              ),
              const SizedBox(height: 8),
              Text(
                'Be the first to share a post here!',
                style: TextStyle(color: Colors.grey[800]),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }
    return ListView.builder(
      padding: const EdgeInsets.symmetric(vertical: 8),
      itemCount: posts.length,
      itemBuilder: (context, index) {
        return PostCard(postData: posts[index]);
      },
    );
  }
}
